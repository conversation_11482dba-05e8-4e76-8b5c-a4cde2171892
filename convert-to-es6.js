#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Convert CommonJS require statements to ES6 imports
 */
function convertRequireToImport(content) {
  // Convert const { ... } = require('...') to import { ... } from '...'
  content = content.replace(
    /const\s+\{\s*([^}]+)\s*\}\s*=\s*require\(['"`]([^'"`]+)['"`]\);?/g,
    "import { $1 } from '$2';"
  );
  
  // Convert const ... = require('...') to import ... from '...'
  content = content.replace(
    /const\s+(\w+)\s*=\s*require\(['"`]([^'"`]+)['"`]\);?/g,
    "import $1 from '$2';"
  );
  
  return content;
}

/**
 * Convert CommonJS exports to ES6 exports
 */
function convertExportsToES6(content) {
  // Convert module.exports = { ... } to export { ... }
  content = content.replace(
    /module\.exports\s*=\s*\{\s*([^}]+)\s*\};?/g,
    "export { $1 };"
  );
  
  // Convert module.exports = ... to export default ...
  content = content.replace(
    /module\.exports\s*=\s*([^;]+);?/g,
    "export default $1;"
  );
  
  return content;
}

/**
 * Add .js extension to relative imports
 */
function addJsExtensions(content) {
  // Add .js to relative imports that don't already have an extension
  content = content.replace(
    /from\s+['"`](\.[^'"`]*?)['"`]/g,
    (match, path) => {
      if (!path.includes('.')) {
        return match.replace(path, path + '.js');
      }
      return match;
    }
  );
  
  return content;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Skip if file doesn't contain CommonJS patterns
    if (!content.includes('require(') && !content.includes('module.exports')) {
      return;
    }
    
    console.log(`Converting: ${filePath}`);
    
    // Apply conversions
    content = convertRequireToImport(content);
    content = convertExportsToES6(content);
    content = addJsExtensions(content);
    
    // Write back to file
    fs.writeFileSync(filePath, content, 'utf8');
    
    console.log(`✓ Converted: ${filePath}`);
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

/**
 * Recursively find and process JavaScript files
 */
function processDirectory(dirPath) {
  const entries = fs.readdirSync(dirPath, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dirPath, entry.name);
    
    if (entry.isDirectory()) {
      processDirectory(fullPath);
    } else if (entry.isFile() && entry.name.endsWith('.js')) {
      processFile(fullPath);
    }
  }
}

// Main execution
const srcPath = path.join(__dirname, 'src');
console.log('Starting CommonJS to ES6 conversion...');
processDirectory(srcPath);
console.log('Conversion completed!');
