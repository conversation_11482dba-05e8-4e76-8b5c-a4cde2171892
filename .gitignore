# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
/.pnp
.pnp.js

# Build output
dist/
build/
out/
/tmp
/out-tsc

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.staging

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# TypeScript cache
*.tsbuildinfo
.tscache/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Database files
*.sqlite
*.sqlite3
*.db

# Temporary files
tmp/
temp/
.tmp/

# SSL certificates and keys
*.pem
*.key
*.crt
*.cert
*.p12
*.pfx

# Backup files
*.backup
*.bak
*.old

# Archive files
*.zip
*.tar.gz
*.rar

# Test coverage
coverage/
.nyc_output/

# PM2 ecosystem file
ecosystem.config.js

# Docker
.dockerignore

# Local development
.local/
local/

# Cache directories
.cache/

# Project specific uploads
uploads/
public/uploads/
storage/
.storage/

# API keys and secrets
secrets/
.secrets/
keys/
.keys/

# Monitoring
.sentry/
newrelic_agent.log

# Package manager lock files (uncomment based on preference)
# package-lock.json  # Uncomment if using yarn exclusively
# yarn.lock          # Uncomment if using npm exclusively

# TypeScript compiled files (be careful with this)
# *.js
# *.js.map
# Keep important config files
!jest.config.js
!webpack.config.js
!gulpfile.js
!rollup.config.js
Dockerfile