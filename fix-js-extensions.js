#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Add .js extension to relative imports that are missing it
 */
function addJsExtensions(content) {
  // Pattern to match import statements with relative paths
  const importPattern = /from\s+['"`](\.[^'"`]*?)['"`]/g;
  
  content = content.replace(importPattern, (match, importPath) => {
    // Skip if already has an extension
    if (path.extname(importPath)) {
      return match;
    }
    
    // Add .js extension
    return match.replace(importPath, importPath + '.js');
  });
  
  // Also handle imports that start with just the filename (same directory)
  const sameDirectoryPattern = /from\s+['"`]([^'"`./][^'"`]*?)['"`]/g;
  
  content = content.replace(sameDirectoryPattern, (match, importPath) => {
    // Skip node_modules and absolute paths
    if (importPath.includes('/') || !importPath.match(/^[a-zA-Z]/)) {
      return match;
    }
    
    // Skip if already has an extension
    if (path.extname(importPath)) {
      return match;
    }
    
    // Add ./ and .js extension for same directory imports
    return match.replace(importPath, './' + importPath + '.js');
  });
  
  return content;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Skip if file doesn't contain import statements
    if (!content.includes('from ')) {
      return;
    }
    
    console.log(`Fixing extensions in: ${filePath}`);
    
    const originalContent = content;
    content = addJsExtensions(content);
    
    // Only write if content changed
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✓ Fixed: ${filePath}`);
    } else {
      console.log(`- No changes needed: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

/**
 * Recursively find and process JavaScript files
 */
function processDirectory(dirPath) {
  const entries = fs.readdirSync(dirPath, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dirPath, entry.name);
    
    if (entry.isDirectory()) {
      processDirectory(fullPath);
    } else if (entry.isFile() && entry.name.endsWith('.js')) {
      processFile(fullPath);
    }
  }
}

// Main execution
const srcPath = path.join(__dirname, 'src');
console.log('Starting .js extension fix...');
processDirectory(srcPath);
console.log('Extension fix completed!');
