import { DataTypes, Model, Op, fn, col  } from 'sequelize';
import { chatDatabase  } from '../../config/database';

/**
 * GuestSession Model
 * Tracks guest user sessions and message counts for rate limiting
 */
class GuestSession extends Model {
  /**
   * Increment message count for this session
   * @returns {Promise<void>}
   */
  async incrementMessageCount() {
    this.messageCount += 1;
    await this.save();
  }

  /**
   * Check if session has reached message limit
   * @param {number} [limit=5] - Message limit
   * @returns {boolean} True if limit reached
   */
  hasReachedLimit(limit = 5) {
    return this.messageCount >= limit;
  }

  /**
   * Get remaining messages for this session
   * @param {number} [limit=5] - Message limit
   * @returns {number} Number of remaining messages
   */
  getRemainingMessages(limit = 5) {
    return Math.max(0, limit - this.messageCount);
  }

  /**
   * Create a new guest session
   * @param {string} sessionId - Session ID
   * @param {string} ipAddress - IP address
   * @returns {Promise<GuestSession>} Created session instance
   */
  static async createSession(sessionId, ipAddress) {
    import { EncryptionUtil  } from '../../utils/encryption';

    return GuestSession.create({
      id: EncryptionUtil.generateUUID(),
      sessionId,
      messageCount: 0,
      ipAddress,
    });
  }

  /**
   * Find session by session ID
   * @param {string} sessionId - Session ID
   * @returns {Promise<GuestSession|null>} Session instance or null
   */
  static async findBySessionId(sessionId) {
    return GuestSession.findOne({
      where: { sessionId },
    });
  }

  /**
   * Find or create a guest session
   * @param {string} sessionId - Session ID
   * @param {string} ipAddress - IP address
   * @returns {Promise<[GuestSession, boolean]>} Tuple of [session, wasCreated]
   */
  static async findOrCreateSession(sessionId, ipAddress) {
    import { EncryptionUtil  } from '../../utils/encryption';

    const [session, created] = await GuestSession.findOrCreate({
      where: { sessionId },
      defaults: {
        id: EncryptionUtil.generateUUID(),
        sessionId,
        messageCount: 0,
        ipAddress,
      },
    });
    return [session, created];
  }

  /**
   * Clean up old guest sessions
   * @param {number} [daysOld=7] - Number of days old to consider for cleanup
   * @returns {Promise<number>} Number of sessions deleted
   */
  static async cleanupOldSessions(daysOld = 7) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    return GuestSession.destroy({
      where: {
        updatedAt: {
          [Op.lt]: cutoffDate,
        },
      },
    });
  }

  /**
   * Get session statistics
   * @returns {Promise<Object>} Session statistics
   */
  static async getSessionStats() {
    const totalSessions = await GuestSession.count();

    const oneDayAgo = new Date();
    oneDayAgo.setDate(oneDayAgo.getDate() - 1);

    const activeSessions = await GuestSession.count({
      where: {
        updatedAt: {
          [Op.gte]: oneDayAgo,
        },
      },
    });

    const result = await GuestSession.findOne({
      attributes: [
        [fn('AVG', col('messageCount')), 'averageMessages'],
      ],
    });

    const averageMessages = parseFloat(result?.get('averageMessages')) || 0;

    return {
      totalSessions,
      activeSessions,
      averageMessages,
    };
  }
}

GuestSession.init(
  {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      allowNull: false,
    },
    sessionId: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
    },
    messageCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    ipAddress: {
      type: DataTypes.STRING(45), // IPv6 compatible
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize: chatDatabase,
    modelName: 'GuestSession',
    tableName: 'guest_sessions',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['session_id'],
      },
      {
        fields: ['message_count'],
      },
      {
        fields: ['ip_address'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['updated_at'],
      },
    ],
  }
);

export { GuestSession  };
